#!/bin/bash

# 部署脚本
echo "开始部署项目到nginx..."

# 设置变量
PROJECT_DIR="/var/www/resume"
NGINX_CONF="/etc/nginx/sites-available/resume"
NGINX_ENABLED="/etc/nginx/sites-enabled/resume"

# 创建项目目录
echo "创建项目目录..."
sudo mkdir -p $PROJECT_DIR

# 复制静态文件
echo "复制静态文件..."
sudo cp index.html $PROJECT_DIR/
sudo cp -r assets $PROJECT_DIR/
sudo cp *.png $PROJECT_DIR/ 2>/dev/null || true

# 设置文件权限
echo "设置文件权限..."
sudo chown -R www-data:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR

# 复制nginx配置
echo "配置nginx..."
sudo cp nginx.conf $NGINX_CONF

# 启用站点
sudo ln -sf $NGINX_CONF $NGINX_ENABLED

# 测试nginx配置
echo "测试nginx配置..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "nginx配置测试通过，重新加载nginx..."
    sudo systemctl reload nginx
    echo "nginx配置已更新"
else
    echo "nginx配置测试失败，请检查配置文件"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Node.js未安装，请先安装Node.js"
    echo "Ubuntu/Debian: sudo apt install nodejs npm"
    echo "CentOS/RHEL: sudo yum install nodejs npm"
    exit 1
fi

# 启动API服务器
echo "启动API服务器..."
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动服务..."
    pm2 start api-server.js --name resume-api
    pm2 save
    pm2 startup
else
    echo "PM2未安装，建议安装PM2来管理Node.js进程："
    echo "npm install -g pm2"
    echo "手动启动API服务器："
    echo "nohup node api-server.js > api.log 2>&1 &"
fi

echo "部署完成！"
echo "访问地址: http://ceshi.njdmkj.cn"
echo "API地址: http://ceshi.njdmkj.cn/api"
