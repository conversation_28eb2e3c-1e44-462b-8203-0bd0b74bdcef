# 部署配置说明

## 环境自动检测

项目现在支持自动检测运行环境，并使用相应的API地址：

### 前端配置 (index.html)

```javascript
const API_CONFIG = {
    // 开发环境
    development: 'http://localhost:3000',
    // 生产环境 - 需要替换为你的服务器地址
    production: 'https://your-server-domain.com'
};
```

### 环境检测规则

- **开发环境**: 当访问地址为 `localhost`、`127.0.0.1` 或空时，使用开发环境配置
- **生产环境**: 其他所有情况使用生产环境配置

## 部署步骤

### 1. 修改生产环境API地址

在 `index.html` 中找到以下代码并修改：

```javascript
const API_CONFIG = {
    development: 'http://localhost:3000',
    production: 'https://your-server-domain.com'  // 👈 修改这里为你的服务器地址
};
```

**示例：**
- 如果你的服务器域名是 `example.com`，端口是 `3000`：
  ```javascript
  production: 'https://example.com:3000'
  ```
- 如果使用IP地址 `*************`，端口是 `8080`：
  ```javascript
  production: 'http://*************:8080'
  ```

### 2. 服务器端配置

#### 环境变量配置

API服务器支持以下环境变量：

- `PORT`: 服务器端口（默认: 3000）
- `HOST`: 监听地址（默认: 0.0.0.0，允许外部访问）

#### 启动方式

**开发环境：**
```bash
node api-server.js
```

**生产环境（使用环境变量）：**
```bash
# 设置端口为8080
PORT=8080 node api-server.js

# 或者在Windows中
set PORT=8080 && node api-server.js
```

**使用PM2管理（推荐生产环境）：**
```bash
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start api-server.js --name "resume-api" -- --port 3000

# 查看状态
pm2 status

# 查看日志
pm2 logs resume-api
```

### 3. 服务器部署注意事项

#### 防火墙设置
确保服务器防火墙允许相应端口访问：
```bash
# Ubuntu/Debian
sudo ufw allow 3000

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3000/tcp
sudo firewall-cmd --reload
```

#### Nginx反向代理（可选）
如果使用Nginx，可以配置反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /path/to/your/html/files;
        index index.html;
        try_files $uri $uri/ =404;
    }
}
```

#### HTTPS配置
如果需要HTTPS支持，建议使用Nginx或其他反向代理来处理SSL证书。

### 4. 测试部署

1. **启动API服务器**
   ```bash
   node api-server.js
   ```

2. **访问测试页面**
   - 开发环境: `http://localhost:5501` 或直接打开 `index.html`
   - 生产环境: `https://your-domain.com`

3. **检查控制台日志**
   打开浏览器开发者工具，查看控制台输出：
   ```
   当前环境检测:
   hostname: your-domain.com
   使用API地址: https://your-domain.com
   ```

### 5. 常见问题

#### CORS错误
如果遇到跨域问题，检查API服务器的CORS设置（已在代码中配置）。

#### 连接失败
1. 确认API服务器正在运行
2. 检查防火墙设置
3. 验证域名/IP地址是否正确
4. 检查端口是否开放

#### HTTPS混合内容错误
如果前端使用HTTPS，API也必须使用HTTPS，或者配置Nginx反向代理。

## Nginx部署步骤

### 1. 服务器准备
```bash
# 安装nginx
sudo apt update
sudo apt install nginx

# 安装Node.js
sudo apt install nodejs npm

# 安装PM2（推荐）
sudo npm install -g pm2
```

### 2. 部署项目
```bash
# 上传项目文件到服务器
# 运行部署脚本
chmod +x deploy.sh
sudo ./deploy.sh
```

### 3. 手动部署步骤
如果不使用脚本，可以手动执行：

```bash
# 1. 创建项目目录
sudo mkdir -p /var/www/resume

# 2. 复制文件
sudo cp index.html /var/www/resume/
sudo cp -r assets /var/www/resume/
sudo cp *.png /var/www/resume/

# 3. 设置权限
sudo chown -R www-data:www-data /var/www/resume
sudo chmod -R 755 /var/www/resume

# 4. 配置nginx
sudo cp nginx.conf /etc/nginx/sites-available/resume
sudo ln -s /etc/nginx/sites-available/resume /etc/nginx/sites-enabled/

# 5. 测试并重载nginx
sudo nginx -t
sudo systemctl reload nginx

# 6. 启动API服务器
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 快速部署检查清单

- [x] 修改 `index.html` 中的 `production` API地址为 `http://ceshi.njdmkj.cn:3000`
- [ ] 上传所有文件到服务器
- [ ] 安装nginx、Node.js、PM2
- [ ] 运行部署脚本或手动部署
- [ ] 启动API服务器：`pm2 start ecosystem.config.js`
- [ ] 配置防火墙允许80和3000端口访问
- [ ] 测试前端页面：`http://ceshi.njdmkj.cn`
- [ ] 检查API调用：`http://ceshi.njdmkj.cn/api`
- [ ] （可选）配置HTTPS证书

完成以上步骤后，你的项目就可以在nginx上正常运行了！
