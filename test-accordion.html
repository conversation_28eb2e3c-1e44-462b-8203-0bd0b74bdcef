<!DOCTYPE html>
<html lang="zh-Hans">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折叠面板测试</title>
    <link href='https://unpkg.com/boxicons@2.1.1/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="assets/css/styles.css">
    <style>
        body {
            padding: 20px;
            background-color: #1a1a2e;
            color: white;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .debug-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>折叠面板功能测试</h1>
        
        <div class="debug-info" id="debug-info">
            等待初始化...
        </div>
        
        <div class="about__expandable">
            <div class="expandable__item">
                <div class="expandable__header" data-target="test-item-1">
                    <h3 class="expandable__title">测试项目 1</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="test-item-1">
                    <div class="expandable__body">
                        <p>这是测试项目1的内容。如果您能看到这段文字，说明折叠面板展开功能正常工作。</p>
                        <p>点击上方的标题可以折叠这个面板。</p>
                    </div>
                </div>
            </div>

            <div class="expandable__item">
                <div class="expandable__header" data-target="test-item-2">
                    <h3 class="expandable__title">测试项目 2</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="test-item-2">
                    <div class="expandable__body">
                        <p>这是测试项目2的内容。</p>
                        <ul>
                            <li>列表项目 1</li>
                            <li>列表项目 2</li>
                            <li>列表项目 3</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="expandable__item">
                <div class="expandable__header" data-target="test-item-3">
                    <h3 class="expandable__title">测试项目 3</h3>
                    <i class='bx bx-chevron-right expandable__icon'></i>
                </div>
                <div class="expandable__content" id="test-item-3">
                    <div class="expandable__body">
                        <p>这是测试项目3的内容。</p>
                        <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 5px;">
                            <strong>重要信息：</strong>
                            <br>如果所有三个面板都能正常展开和折叠，说明JavaScript功能正常工作。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <button onclick="testAllPanels()" style="margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
            测试所有面板
        </button>
    </div>

    <script>
        // 初始化折叠面板功能
        function initExpandableItems() {
            const expandableHeaders = document.querySelectorAll('.expandable__header');
            
            expandableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);
                    const icon = this.querySelector('.expandable__icon');
                    const item = this.closest('.expandable__item');
                    
                    if (targetContent && icon && item) {
                        // 切换展开/折叠状态
                        const isActive = item.classList.contains('active');
                        
                        if (isActive) {
                            // 折叠
                            item.classList.remove('active');
                            icon.classList.remove('bx-chevron-down');
                            icon.classList.add('bx-chevron-right');
                            updateDebugInfo(`折叠: ${targetId}`);
                        } else {
                            // 展开
                            item.classList.add('active');
                            icon.classList.remove('bx-chevron-right');
                            icon.classList.add('bx-chevron-down');
                            updateDebugInfo(`展开: ${targetId}`);
                        }
                    }
                });
            });
            
            updateDebugInfo('✅ 折叠面板功能已初始化，找到 ' + expandableHeaders.length + ' 个面板');
        }

        function updateDebugInfo(message) {
            const debugInfo = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML = `[${timestamp}] ${message}`;
        }

        function testAllPanels() {
            const headers = document.querySelectorAll('.expandable__header');
            let index = 0;
            
            function clickNext() {
                if (index < headers.length) {
                    headers[index].click();
                    index++;
                    setTimeout(clickNext, 500);
                } else {
                    updateDebugInfo('✅ 所有面板测试完成');
                }
            }
            
            updateDebugInfo('🔄 开始自动测试所有面板...');
            clickNext();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initExpandableItems();
        });
    </script>
</body>
</html>
