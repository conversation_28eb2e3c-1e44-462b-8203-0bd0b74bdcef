<!DOCTYPE html>
<html lang="zh-<PERSON>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>开发环境测试页面</h1>
        
        <div class="status info">
            <strong>环境检测:</strong> <span id="environment">检测中...</span>
        </div>
        
        <div class="status info">
            <strong>API地址:</strong> <span id="api-url">检测中...</span>
        </div>
        
        <button onclick="testAPI()">测试API调用</button>
        <button onclick="testCompanyIntro()">测试企业介绍</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        // API配置
        const API_CONFIG = {
            development: 'http://localhost:3001/api',
            production: 'https://www.kunpeng360.com/CustomerQuery/Query?t=@t@&barcode=@barcode@'
        };

        // 环境检测
        function isProductionEnvironment() {
            return window.location.hostname !== 'localhost' &&
                   window.location.hostname !== '127.0.0.1' &&
                   window.location.hostname !== '';
        }

        // 获取API地址
        const API_BASE_URL = isProductionEnvironment() ? API_CONFIG.production : API_CONFIG.development;

        // 页面加载时显示环境信息
        document.addEventListener('DOMContentLoaded', function() {
            const envElement = document.getElementById('environment');
            const apiElement = document.getElementById('api-url');
            
            if (isProductionEnvironment()) {
                envElement.textContent = '生产环境';
                envElement.parentElement.className = 'status error';
            } else {
                envElement.textContent = '开发环境';
                envElement.parentElement.className = 'status success';
            }
            
            apiElement.textContent = API_BASE_URL;
        });

        // 显示结果
        function showResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // 测试主API
        async function testAPI() {
            showResult('开始测试', '正在调用主API...', 'info');
            
            try {
                const response = await fetch(API_BASE_URL, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                showResult('API调用成功', result, 'success');
                
            } catch (error) {
                showResult('API调用失败', error.message, 'error');
            }
        }

        // 测试企业介绍API
        async function testCompanyIntro() {
            showResult('开始测试', '正在调用企业介绍API...', 'info');
            
            try {
                const introUrl = isProductionEnvironment() 
                    ? 'https://www.kunpeng360.com/CustomerQuery/DocHtml?t=05580002&docId=688dd2445545dbf1f62328c4'
                    : 'http://localhost:3001/api/company-intro?t=05580002&docId=688dd2445545dbf1f62328c4';
                
                const response = await fetch(introUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/html',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.text();
                showResult('企业介绍API调用成功', result.substring(0, 500) + '...', 'success');
                
            } catch (error) {
                showResult('企业介绍API调用失败', error.message, 'error');
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
